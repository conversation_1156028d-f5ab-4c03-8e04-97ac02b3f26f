import React, { useEffect, useImperativeHandle, useRef } from 'react';

/**
 * Componente de integración opcional para WaterTankForm
 * Proporciona comunicación bidireccional con el sistema de monitoreo global
 * 
 * NOTA: Este es un ejemplo de cómo se puede integrar opcionalmente con WaterTankForm.jsx
 * No es necesario para el funcionamiento básico del sistema de monitoreo.
 */
const WaterTankFormIntegration = ({ 
  waterTanks, 
  onTankOperationStart, 
  onTankOperationComplete 
}) => {
  const monitorRef = useRef(null);

  // Función para notificar al monitor global cuando se inicia una operación desde WaterTankForm
  const notifyOperationStart = (tank) => {
    console.log('[WaterTankFormIntegration] Notificando inicio de operación:', tank.name);
    
    // Crear evento personalizado para comunicar con el monitor global
    const event = new CustomEvent('waterTankOperationStart', {
      detail: {
        tank: tank,
        source: 'WaterTankForm'
      }
    });
    
    window.dispatchEvent(event);
    
    // Callback opcional
    if (onTankOperationStart) {
      onTankOperationStart(tank);
    }
  };

  // Función para manejar cuando una operación se completa (detectada por el monitor global)
  const handleOperationComplete = (event) => {
    const { tankId, tankName, action, completionReason } = event.detail;
    
    console.log('[WaterTankFormIntegration] Operación completada detectada:', {
      tankId,
      tankName,
      action,
      completionReason
    });
    
    // Callback opcional para actualizar UI local
    if (onTankOperationComplete) {
      onTankOperationComplete({
        tankId,
        tankName,
        action,
        completionReason
      });
    }
  };

  // Configurar listeners para eventos del monitor global
  useEffect(() => {
    // Listener para operaciones completadas
    window.addEventListener('waterTankOperationComplete', handleOperationComplete);
    
    return () => {
      window.removeEventListener('waterTankOperationComplete', handleOperationComplete);
    };
  }, [onTankOperationComplete]);

  // Función pública para ser llamada desde WaterTankForm cuando se ejecuta una operación
  const handleExecuteOperation = (tank) => {
    notifyOperationStart(tank);
  };

  // Función pública para ser llamada desde WaterTankForm cuando se detiene manualmente una operación
  const handleStopOperation = (tank) => {
    console.log('[WaterTankFormIntegration] Notificando detención manual:', tank.name);
    
    const event = new CustomEvent('waterTankOperationStop', {
      detail: {
        tank: tank,
        source: 'WaterTankForm',
        reason: 'manual'
      }
    });
    
    window.dispatchEvent(event);
  };

  // Exponer funciones públicas
  useImperativeHandle(monitorRef, () => ({
    handleExecuteOperation,
    handleStopOperation
  }));

  // Este componente no renderiza nada
  return null;
};

// Hook personalizado para usar la integración en WaterTankForm
export const useWaterTankFormIntegration = () => {
  const integrationRef = useRef(null);

  const notifyOperationStart = (tank) => {
    if (integrationRef.current) {
      integrationRef.current.handleExecuteOperation(tank);
    }
  };

  const notifyOperationStop = (tank) => {
    if (integrationRef.current) {
      integrationRef.current.handleStopOperation(tank);
    }
  };

  return {
    integrationRef,
    notifyOperationStart,
    notifyOperationStop,
    IntegrationComponent: WaterTankFormIntegration
  };
};

export default WaterTankFormIntegration;

