import React, { useState } from 'react';
import DigestFetch from 'digest-fetch';
import {
  Card,
  CardContent,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  Alert,
  CircularProgress,
  InputAdornment,
  Divider,
  Paper
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import {
  CameraAlt as CameraIcon,
  SwapHoriz as HorizontalIcon,
  SwapVert as VerticalIcon
} from '@material-ui/icons';

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(3),
    backgroundColor: theme.palette.background.default,
  },
  streamCard: {
    marginBottom: theme.spacing(3),
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
    border: '2px solid #e0e0e0',
    borderRadius: theme.shape.borderRadius,
  },
  streamContainer: {
    position: 'relative',
    backgroundColor: '#000',
    borderRadius: theme.shape.borderRadius,
    overflow: 'hidden',
    minHeight: '300px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  streamImage: {
    maxWidth: '100%',
    maxHeight: '500px',
    height: 'auto',
    display: 'block',
  },
  controlsCard: {
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e0e0e0',
    borderRadius: theme.shape.borderRadius,
  },
  controlSection: {
    padding: theme.spacing(2),
  },
  controlButton: {
    margin: theme.spacing(1),
    minWidth: '120px',
    fontWeight: 'bold',
  },
  snapshotButton: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.dark,
    },
  },
  movementButton: {
    backgroundColor: '#2196f3',
    color: 'white',
    '&:hover': {
      backgroundColor: '#1976d2',
    },
  },
  degreeInput: {
    margin: theme.spacing(1),
    minWidth: '100px',
  },
  title: {
    marginBottom: theme.spacing(2),
    fontWeight: 'bold',
    color: theme.palette.text.primary,
  },
  sectionTitle: {
    marginBottom: theme.spacing(1),
    fontWeight: 'bold',
    color: theme.palette.text.secondary,
  },
  errorAlert: {
    marginTop: theme.spacing(2),
  },
  loadingContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '200px',
    color: '#fff',
  },
}));

export const CameraTesting = () => {
  const classes = useStyles();
  const [imgSrc, setImgSrc] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [horizontalDegrees, setHorizontalDegrees] = useState(10);
  const [verticalDegrees, setVerticalDegrees] = useState(10);
  const [movementLoading, setMovementLoading] = useState({ horizontal: false, vertical: false });

  const api_snapshot_request = process.env.REACT_APP_GET_SNAPSHOT_FROM_CAMERA;
  const api_http_stream = process.env.REACT_APP_GET_HTTP_STREAM_FROM_CAMERA;
  const user = process.env.REACT_APP_CAMERA_USER;
  const pass = process.env.REACT_APP_CAMERA_PASS;

  const handleFetchImage = async () => {
    setLoading(true);
    setError(null);
    try {
      // Creamos un cliente DigestFetch con usuario y contraseña
      const client = new DigestFetch(user, pass, { algorithm: 'MD5', qop: 'auth' });

      // Al llamar a client.fetch, internamente hará el 401 inicial,
      // leerá el WWW-Authenticate y volverá a intentar con la cabecera correcta.
      const response = await client.fetch(api_snapshot_request);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status} – ${response.statusText}`);
      }
      const blob = await response.blob();
      setImgSrc(URL.createObjectURL(blob));
    } catch (err) {
      console.error(err);
      setError('No se pudo cargar la imagen: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleHorizontalMovement = async () => {
    setMovementLoading(prev => ({ ...prev, horizontal: true }));
    setError(null);
    try {
      // Aquí implementarías la lógica para mover la cámara horizontalmente
      const move_horizontal_api = process.env.REACT_APP_MOVE_HORIZONTAL_API + horizontalDegrees;
      // Creamos un cliente DigestFetch con usuario y contraseña
      const client = new DigestFetch(user, pass, { algorithm: 'MD5', qop: 'auth' });
      const response = await client.fetch(move_horizontal_api);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status} – ${response.statusText}`);
      }
      // // Por ahora simularemos la operación
      // await new Promise(resolve => setTimeout(resolve, 1000));
      // console.log(`Moviendo cámara horizontalmente ${horizontalDegrees} grados`);
    } catch (err) {
      console.error(err);
      setError('Error al mover la cámara horizontalmente: ' + err.message);
    } finally {
      setMovementLoading(prev => ({ ...prev, horizontal: false }));
    }
  };

  const handleVerticalMovement = async () => {
    setMovementLoading(prev => ({ ...prev, vertical: true }));
    setError(null);
    try {
      // Aquí implementarías la lógica para mover la cámara verticalmente
      const move_vertical_api = process.env.REACT_APP_MOVE_VERTICAL_API + verticalDegrees;
      const client = new DigestFetch(user, pass, { algorithm: 'MD5', qop: 'auth' });
      const response = await client.fetch(move_vertical_api);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status} – ${response.statusText}`);
      }
      // Por ahora simularemos la operación
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log(`Moviendo cámara verticalmente ${verticalDegrees} grados`);
    } catch (err) {
      console.error(err);
      setError('Error al mover la cámara verticalmente: ' + err.message);
    } finally {
      setMovementLoading(prev => ({ ...prev, vertical: false }));
    }
  };

  return (
    <div className={classes.root}>
      <Typography variant="h4" className={classes.title} align="center">
        Control de Videocámara
      </Typography>

      {/* Stream de la cámara */}
      <Card className={classes.streamCard}>
        <CardContent>
          <Typography variant="h6" className={classes.sectionTitle} align="center">
            Stream en Vivo
          </Typography>
          <Box className={classes.streamContainer}>
            {api_http_stream ? (
              <img
                src={api_http_stream}
                alt="Stream de la cámara"
                className={classes.streamImage}
                onError={(e) => {
                  e.target.style.display = 'none';
                  setError('Error al cargar el stream de la cámara');
                }}
              />
            ) : (
              <div className={classes.loadingContainer}>
                <Typography variant="body1">
                  Stream no disponible
                </Typography>
              </div>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Controles de la cámara */}
      <Card className={classes.controlsCard}>
        <CardContent>
          <Typography variant="h6" className={classes.sectionTitle} align="center">
            Controles de Cámara
          </Typography>

          {/* Botón de Snapshot */}
          <Box className={classes.controlSection}>
            <Typography variant="subtitle1" className={classes.sectionTitle}>
              Captura de Imagen
            </Typography>
            <Grid container justifyContent="center">
              <Button
                variant="contained"
                className={`${classes.controlButton} ${classes.snapshotButton}`}
                onClick={handleFetchImage}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CameraIcon />}
              >
                {loading ? 'Capturando...' : 'Tomar Snapshot'}
              </Button>
            </Grid>
          </Box>

          <Divider />

          {/* Controles de movimiento */}
          <Box className={classes.controlSection}>
            <Typography variant="subtitle1" className={classes.sectionTitle}>
              Control de Movimiento
            </Typography>

            <Grid container spacing={3} justifyContent="center">
              {/* Movimiento Horizontal */}
              <Grid item xs={12} sm={6}>
                <Paper elevation={1} style={{ padding: '16px' }}>
                  <Typography variant="body2" align="center" gutterBottom>
                    Movimiento Horizontal
                  </Typography>
                  <Grid container spacing={2} alignItems="center" justifyContent="center">
                    <Grid item>
                      <TextField
                        className={classes.degreeInput}
                        type="number"
                        label="Grados"
                        variant="outlined"
                        size="small"
                        value={horizontalDegrees}
                        onChange={(e) => setHorizontalDegrees(Number(e.target.value))}
                        inputProps={{ min: -90, max: 360 }}
                        InputProps={{
                          endAdornment: <InputAdornment position="end">°</InputAdornment>,
                        }}
                      />
                    </Grid>
                    <Grid item>
                      <Button
                        variant="contained"
                        className={`${classes.controlButton} ${classes.movementButton}`}
                        onClick={handleHorizontalMovement}
                        disabled={movementLoading.horizontal}
                        startIcon={movementLoading.horizontal ? <CircularProgress size={20} color="inherit" /> : <HorizontalIcon />}
                      >
                        {movementLoading.horizontal ? 'Moviendo...' : 'Mover'}
                      </Button>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* Movimiento Vertical */}
              <Grid item xs={12} sm={6}>
                <Paper elevation={1} style={{ padding: '16px' }}>
                  <Typography variant="body2" align="center" gutterBottom>
                    Movimiento Vertical
                  </Typography>
                  <Grid container spacing={2} alignItems="center" justifyContent="center">
                    <Grid item>
                      <TextField
                        className={classes.degreeInput}
                        type="number"
                        label="Grados"
                        variant="outlined"
                        size="small"
                        value={verticalDegrees}
                        onChange={(e) => setVerticalDegrees(Number(e.target.value))}
                        inputProps={{ min: -90, max: 360 }}
                        InputProps={{
                          endAdornment: <InputAdornment position="end">°</InputAdornment>,
                        }}
                      />
                    </Grid>
                    <Grid item>
                      <Button
                        variant="contained"
                        className={`${classes.controlButton} ${classes.movementButton}`}
                        onClick={handleVerticalMovement}
                        disabled={movementLoading.vertical}
                        startIcon={movementLoading.vertical ? <CircularProgress size={20} color="inherit" /> : <VerticalIcon />}
                      >
                        {movementLoading.vertical ? 'Moviendo...' : 'Mover'}
                      </Button>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>

      {/* Mostrar errores */}
      {/* {error && (
        <Alert severity="error" className={classes.errorAlert}>
          {error}
        </Alert>
      )} */}

      {/* Mostrar imagen capturada */}
      {imgSrc && (
        <Card className={classes.streamCard} style={{ marginTop: '24px' }}>
          <CardContent>
            <Typography variant="h6" className={classes.sectionTitle} align="center">
              Última Captura
            </Typography>
            <Box className={classes.streamContainer}>
              <img
                src={imgSrc}
                alt="Snapshot de la cámara"
                className={classes.streamImage}
              />
            </Box>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
