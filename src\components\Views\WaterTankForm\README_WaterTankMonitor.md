# Sistema de Monitoreo Global de Tanques de Agua

## Descripción General

Este sistema proporciona monitoreo en tiempo real de las operaciones de llenado y vaciado de tanques de agua, funcionando independientemente de si el usuario está en la sección `WaterTankForm` o no. Cuando una operación termina, el sistema actualiza automáticamente los estados en Firebase y notifica al usuario.

## Componentes del Sistema

### 1. WaterTankMonitor.jsx
**Propósito**: Componente principal que monitorea las operaciones en segundo plano.

**Funcionalidades**:
- Detecta tanques con operaciones activas al cargar
- Monitorea switches de nivel (máximo/mínimo) para detectar finalización
- Escucha confirmaciones MQTT desde Firebase
- Configura timeouts basados en el tiempo total de operación
- Actualiza automáticamente `currentStatus` y `lastExecution` en Firebase
- Envía notificaciones cuando las operaciones terminan

**Métodos de detección de finalización**:
1. **Por Switch**: <PERSON>uan<PERSON> el switch de nivel máximo se activa (llenado) o el mínimo se desactiva (vaciado)
2. **Por Timeout**: Cuando se cumple el tiempo total configurado para la operación
3. **Por Firebase**: Cuando se recibe confirmación MQTT de detención (accion=244, val=0)

### 2. useWaterTankNotifications.js
**Propósito**: Hook personalizado para manejar notificaciones.

**Funcionalidades**:
- Gestiona el estado de notificaciones actuales e historial
- Persistencia local usando localStorage
- Límite máximo de 50 notificaciones
- Auto-ocultado de notificaciones después de 6 segundos
- Funciones para agregar, remover y limpiar notificaciones

### 3. WaterTankNotification.jsx
**Propósito**: Componente visual para mostrar notificaciones emergentes.

**Características**:
- Snackbar con diseño Material-UI
- Diferentes tipos de alerta (success, error, warning, info)
- Badges para identificar tipo de operación (llenado/vaciado)
- Información del tanque y timestamp
- Botón de cierre manual

### 4. WaterTankNotificationHistory.jsx
**Propósito**: Diálogo para mostrar historial completo de notificaciones.

**Características**:
- Lista ordenada por timestamp (más recientes primero)
- Información detallada de cada notificación
- Botones para eliminar notificaciones individuales
- Opción para limpiar todo el historial
- Formateo de fechas con timezone del usuario

### 5. WaterTankGlobalMonitor.jsx
**Propósito**: Componente contenedor que integra todo el sistema.

**Funcionalidades**:
- Integra el monitor con las notificaciones
- Botón flotante para acceder al historial
- Manejo de estados de diálogos
- Solo se renderiza si hay usuario autenticado

## Integración en la Aplicación

El sistema se integra a nivel de aplicación en `App.jsx`:

```jsx
import WaterTankGlobalMonitor from "./components/Views/WaterTankForm/WaterTankGlobalMonitor";

// Dentro del componente App
<WaterTankGlobalMonitor />
```

## Flujo de Funcionamiento

1. **Inicialización**: Al cargar la aplicación, `WaterTankMonitor` busca tanques con `currentStatus.isRunning = true`
2. **Monitoreo Activo**: Para cada tanque activo, se configuran:
   - Listener de switches
   - Listener de confirmaciones MQTT
   - Timeout basado en tiempo total
3. **Detección de Finalización**: Cuando cualquiera de los métodos detecta finalización:
   - Se actualiza `currentStatus.isRunning = false` en Firebase
   - Se actualiza `lastExecution` con timestamp actual
   - Se envía notificación al usuario
   - Se limpia el monitoreo del tanque
4. **Notificación**: La notificación aparece como Snackbar y se guarda en historial
5. **Persistencia**: El historial se mantiene en localStorage entre sesiones

## Estructura de Datos

### currentStatus en Firebase
```javascript
{
  isRunning: boolean,
  typeOfAction: "fill" | "empty" | "recirculate" | null
}
```

### Estructura de Notificación
```javascript
{
  id: number,
  timestamp: string (ISO),
  type: "success" | "error" | "warning" | "info",
  title: string,
  message: string,
  category: "water-tank",
  tankId: number,
  tankName: string,
  action: "fill" | "empty",
  autoHide: boolean
}
```

## Configuración y Personalización

### Timeouts
- Los timeouts se calculan basándose en `totalMinutes` y `totalSeconds` de cada operación
- Se configuran automáticamente al iniciar el monitoreo

### Notificaciones
- Auto-ocultado: 6 segundos (configurable)
- Máximo en historial: 50 notificaciones
- Persistencia: localStorage con clave `waterTankNotifications`

### Switches Monitoreados
- **Llenado**: `tank.fillData.maxSwitch` (finaliza cuando state = 1)
- **Vaciado**: `tank.emptyData.minSwitch` (finaliza cuando state = 0)

## Ventajas del Sistema

1. **Independiente de UI**: Funciona aunque el usuario cambie de sección
2. **Múltiples Métodos de Detección**: Redundancia para mayor confiabilidad
3. **Actualización Automática**: Estados siempre sincronizados en Firebase
4. **Notificaciones Persistentes**: Historial disponible entre sesiones
5. **Integración Transparente**: No requiere cambios en componentes existentes

## Consideraciones de Rendimiento

- Los listeners se crean/destruyen dinámicamente según tanques activos
- Cleanup automático al desmontar componentes
- Límite en historial de notificaciones para evitar uso excesivo de memoria
- Uso eficiente de localStorage con manejo de errores

## Debugging

El sistema incluye logs detallados con prefijo `[WaterTankMonitor]` para facilitar el debugging:
- Inicio/fin de monitoreo de tanques
- Detección de finalización por diferentes métodos
- Actualizaciones en Firebase
- Errores y excepciones
