import React, { useContext, useState } from 'react';
import { useLocation } from 'react-router-dom';
import WaterTankMonitor from './WaterTankMonitor';
import WaterTankNotification from './WaterTankNotification';
import WaterTankNotificationHistory from './WaterTankNotificationHistory';
import useWaterTankNotifications from '../../../hooks/useWaterTankNotifications';
import { UserContext } from '../../../context/UserProvider';
import { Fab, Tooltip } from '@material-ui/core';
import { Notifications as NotificationsIcon } from '@material-ui/icons';
import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  notificationFab: {
    position: 'fixed',
    bottom: theme.spacing(2),
    right: theme.spacing(2),
    zIndex: theme.zIndex.speedDial,
  },
}));

/**
 * Componente global que integra el monitoreo de tanques de agua y las notificaciones
 * Este componente debe ser montado a nivel de aplicación para funcionar globalmente
 */
const WaterTankGlobalMonitor = () => {
  const classes = useStyles();
  const location = useLocation();
  const { usuario, userTimezone } = useContext(UserContext);
  const [showHistory, setShowHistory] = useState(false);

  const {
    notifications,
    currentNotification,
    notifyWaterTankOperation,
    hideCurrentNotification,
    hasCurrentNotification,
    removeNotification,
    clearAllNotifications
  } = useWaterTankNotifications();

  // Solo renderizar si hay un usuario autenticado
  if (!usuario || !usuario.username) {
    return null;
  }

  // Función para manejar notificaciones del monitor
  const handleMonitorNotification = (notificationData) => {
    console.log('[WaterTankGlobalMonitor] Recibida notificación del monitor:', notificationData);
    notifyWaterTankOperation(notificationData);
  };

  // Función para cerrar la notificación actual
  const handleCloseNotification = () => {
    hideCurrentNotification();
  };

  // Función para abrir el historial de notificaciones
  const handleOpenHistory = () => {
    setShowHistory(true);
  };

  // Función para cerrar el historial de notificaciones
  const handleCloseHistory = () => {
    setShowHistory(false);
  };

  // Obtener notificaciones de tanques de agua
  const waterTankNotifications = notifications.filter(n => n.category === 'water-tank');

  // Verificar si el usuario está en la sección de tanques de agua
  const isInWaterTankSection = location.pathname === '/water-tank';

  return (
    <>
      {/* Monitor de tanques de agua - funciona en segundo plano */}
      <WaterTankMonitor onNotification={handleMonitorNotification} />

      {/* Componente de notificación visual */}
      <WaterTankNotification
        notification={currentNotification}
        open={hasCurrentNotification}
        onClose={handleCloseNotification}
        autoHideDuration={6000}
      />

      {/* Botón flotante para mostrar historial de notificaciones - Solo en sección de tanques */}
      {waterTankNotifications.length > 0 && isInWaterTankSection && (
        <Tooltip title="Ver historial de notificaciones de tanques">
          <Fab
            color="primary"
            size="small"
            className={classes.notificationFab}
            onClick={handleOpenHistory}
          >
            <NotificationsIcon />
          </Fab>
        </Tooltip>
      )}

      {/* Historial de notificaciones */}
      <WaterTankNotificationHistory
        open={showHistory}
        onClose={handleCloseHistory}
        notifications={waterTankNotifications}
        onRemoveNotification={removeNotification}
        onClearAll={clearAllNotifications}
        userTimezone={userTimezone}
      />
    </>
  );
};

export default WaterTankGlobalMonitor;
